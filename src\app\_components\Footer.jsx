"use client";

import { Button, Input } from "@relume_io/relume-ui";
import Link from "next/link";
import React, { useState } from "react";
import {
  BiLogoFacebookCircle,
  BiLogoInstagram,
  BiLogoLinkedinSquare,
} from "react-icons/bi";

const useForm = () => {
  const [email, setEmail] = useState("");
  const handleSetEmail = (event) => {
    setEmail(event.target.value);
  };
  const handleSubmit = (event) => {
    event.preventDefault();
    console.log({ email });
  };
  return {
    email,
    handleSetEmail,
    handleSubmit,
  };
};

export function Footer2() {
  const formState = useForm();
  return (
    <footer id="relume" className="bg-background-primary px-[5%] py-16 md:py-24 lg:py-28 border-t border-border">
      <div className="container">
        <div className="grid grid-cols-1 items-start gap-x-[8vw] gap-y-12 pb-12 md:gap-y-16 md:pb-18 lg:grid-cols-[1fr_0.5fr] lg:gap-y-4 lg:pb-20">
          <div className="grid grid-cols-1 items-start gap-x-8 gap-y-10 sm:grid-cols-3 sm:gap-x-6 sm:gap-y-12 md:gap-x-8 lg:grid-cols-4">
            <div className="flex flex-col items-start justify-start gap-4">
              <div className="flex flex-col gap-3 mb-4">
                <Link href="/forestforward" className="hover:opacity-80 transition-opacity">
                  <img
                    src="/brand/forestforward/icon.jpg"
                    className="rounded-lg w-10 h-10 object-cover transition-all duration-300 hover:shadow-md"
                    alt="Forest Forward Logo"
                  />
                </Link>
                <Link href="/storyforward" className="hover:opacity-80 transition-opacity">
                  <img
                    src="/brand/storyforward/icon.jpg"
                    className="rounded-lg w-10 h-10 object-cover transition-all duration-300 hover:shadow-md"
                    alt="Story Forward Logo"
                  />
                </Link>
                <Link href="/lagom" className="hover:opacity-80 transition-opacity">
                  <img
                    src="/brand/lagom/icon.jpg"
                    className="rounded-lg w-10 h-10 object-cover transition-all duration-300 hover:shadow-md"
                    alt="Lagom Events Logo"
                  />
                </Link>
              </div>
              <ul className="space-y-1">
                <li>
                  <Link href="/over-ons" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Over ons
                  </Link>
                </li>
                <li>
                  <Link href="/blogs" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Blogs
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Contact
                  </Link>
                </li>
              </ul>
            </div>
            <div className="flex flex-col items-start justify-start">
              <h2 className="mb-4 text-lg font-semibold text-text-primary md:mb-5">Forest Forward</h2>
              <ul className="space-y-1">
                <li>
                  <Link href="/forestforward/bedrijfsbos" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Bedrijfsbos
                  </Link>
                </li>
                <li>
                  <Link href="/forestforward/schoolbos" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Schoolbos
                  </Link>
                </li>
                <li>
                  <Link href="/forestforward/voedselbos" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Voedselbos
                  </Link>
                </li>
                <li>
                  <Link href="/forestforward/natuuropwaardering" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Natuuropwaardering
                  </Link>
                </li>
                <li>
                  <Link href="/forestforward/dakboerderij" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Dakboerderij
                  </Link>
                </li>
                <li>
                  <Link href="/forestforward/start2forest" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Start2Forest
                  </Link>
                </li>
                <li>
                  <Link href="/forestforward/boscompensatie" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Boscompensatie
                  </Link>
                </li>
                <li>
                  <Link href="/forestforward/visie" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Visie
                  </Link>
                </li>
              </ul>
            </div>

            <div className="flex flex-col items-start justify-start">
              <h2 className="mb-4 text-lg font-semibold text-text-primary md:mb-5">Story Forward</h2>
              <ul className="space-y-1">
                <li>
                  <Link href="/storyforward/strategische-communicatie" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Strategische communicatie
                  </Link>
                </li>
                <li>
                  <Link href="/storyforward/storytelling" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Storytelling
                  </Link>
                </li>
                <li>
                  <Link href="/storyforward/mediarelaties" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Mediarelaties
                  </Link>
                </li>
                <li>
                  <Link href="/storyforward/trainingen-workshops" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Trainingen & workshops
                  </Link>
                </li>
                <li>
                  <Link href="/storyforward/design-branding-web" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Design, branding & web
                  </Link>
                </li>
                <li>
                  <Link href="/storyforward/cases" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Cases
                  </Link>
                </li>
                <li>
                  <Link href="/storyforward/visie" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Visie
                  </Link>
                </li>
                <li>
                  <Link href="/storyforward/newsroom" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Newsroom
                  </Link>
                </li>
              </ul>
            </div>
            <div className="flex flex-col items-start justify-start">
              <h2 className="mb-4 text-lg font-semibold text-text-primary md:mb-5">Lagom Events</h2>
              <ul className="space-y-1">
                <li>
                  <Link href="/lagom/impact-events" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Impact events
                  </Link>
                </li>
                <li>
                  <Link href="/lagom/familiedagen" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Familiedagen
                  </Link>
                </li>
                <li>
                  <Link href="/lagom/workshops" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Duurzame talks & walks
                  </Link>
                </li>
                <li>
                  <Link href="/lagom/visie" className="text-sm text-text-primary hover:text-link-secondary transition-colors duration-300 py-2 block">
                    Visie
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="flex flex-col bg-background rounded-lg p-6 md:p-8 shadow-sm border border-border">
            <h2 className="mb-4 text-lg font-semibold text-text-primary md:mb-5">Nieuwsbrief</h2>
            <p className="mb-5 text-sm text-text-secondary md:mb-6">
              Schrijf je in op de nieuwsbrief
            </p>
            <div className="w-full max-w-md">
              <form
                className="mb-4 grid grid-cols-1 gap-x-4 gap-y-3 sm:grid-cols-[1fr_max-content] md:gap-y-4"
                onSubmit={formState.handleSubmit}
              >
                <Input
                  id="email"
                  type="email"
                  placeholder="Emailadres"
                  value={formState.email}
                  onChange={formState.handleSetEmail}
                  className="border-border-primary rounded-lg focus:border-link-primary transition-colors duration-300"
                />
                <Button
                  title="Inschrijven"
                  className="bg-link hover:bg-link-primary text-text-alternative border-none rounded-lg transition-colors duration-300"
                  size="sm"
                >
                  Inschrijven
                </Button>
              </form>
              <p className="text-xs text-text-secondary leading-relaxed">
                Bij het inschrijven op de nieuwsbrief ga je akkoord op de
                privacy policy van WeForward BV.
              </p>
            </div>
          </div>
        </div>
        <div className="h-px w-full bg-border-primary" />
        <div className="flex flex-col-reverse items-start pt-8 pb-4 text-sm md:justify-start md:pt-10 md:pb-0 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex flex-col-reverse items-start md:flex-row md:gap-6 lg:items-center">
            <div className="grid grid-flow-row grid-cols-[max-content] justify-center gap-y-4 md:grid-flow-col md:justify-center md:gap-x-6 md:gap-y-0 lg:text-left">
              <p className="mt-8 md:mt-0 text-text-secondary">© 2025 Built by Léon Missoul.</p>
              <p className="mt-8 md:mt-0 text-text-secondary">WeForward BV</p>
              <p className="mt-8 md:mt-0 text-text-secondary">Kruisvest 5B, 8000 Brugge</p>
              <p className="mt-8 md:mt-0 text-text-secondary">BE 1004.644.044</p>
            </div>
          </div>
          <div className="mb-8 flex items-center justify-center gap-4 lg:mb-0">
            <p>Volg ons op: </p>
            <a
              href="https://facebook.com/weforward"
              target="_blank"
              rel="noopener noreferrer"
              className="text-text-primary hover:text-link-secondary transition-colors duration-300"
              aria-label="Follow us on Facebook"
            >
              <BiLogoFacebookCircle className="size-6" />
            </a>
            <a
              href="https://instagram.com/weforward"
              target="_blank"
              rel="noopener noreferrer"
              className="text-text-primary hover:text-link-secondary transition-colors duration-300"
              aria-label="Follow us on Instagram"
            >
              <BiLogoInstagram className="size-6" />
            </a>
            <a
              href="https://linkedin.com/company/weforward"
              target="_blank"
              rel="noopener noreferrer"
              className="text-text-primary hover:text-link-secondary transition-colors duration-300"
              aria-label="Follow us on LinkedIn"
            >
              <BiLogoLinkedinSquare className="size-6" />
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}
