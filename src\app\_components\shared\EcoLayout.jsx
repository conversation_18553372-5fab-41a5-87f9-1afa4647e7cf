"use client";

import { motion } from "framer-motion";
import React, { useState } from "react";
import { Button1 } from "./Button1";

export function Layout394() {
  const [hoveredCard, setHoveredCard] = useState(null);
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28 bg-white">
      <div className="container">
        <motion.div
          className="mx-auto mb-12 w-full max-w-lg text-center md:mb-18 lg:mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <h1 className="mb-5 text-5xl font-semibold md:mb-6 md:text-7xl lg:text-8xl">
            Ons ecosysteem
          </h1>
        </motion.div>
        <motion.div
          className="grid auto-cols-fr grid-cols-1 gap-6 md:gap-8 lg:grid-cols-3"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          variants={{
            hidden: {},
            visible: {
              transition: {
                staggerChildren: 0.2
              }
            }
          }}
        >
          <motion.div
            className="group relative flex flex-col border border-border rounded-lg overflow-hidden cursor-pointer"
            variants={{
              hidden: { opacity: 0, y: 30 },
              visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
            }}
            whileHover={{
              scale: 1.02,
              transition: { duration: 0.3, ease: "easeOut" }
            }}
            onMouseEnter={() => setHoveredCard('forestforward')}
            onMouseLeave={() => setHoveredCard(null)}
          >
            <div className="flex flex-1 flex-col justify-center p-6 md:p-8">
              <div>
                <img
                  src="/brand/forestforward/full_logo_cut.png"
                  alt="Forest Forward Logo"
                  className="h-12 md:h-12 lg:h-14 w-auto object-contain"
                />
                <h2 className="mb-3 mt-3 text-2xl font-semibold md:mb-4 md:text-3xl md:leading-[1.3] lg:text-4xl">
                  DOE
                </h2>
                <p>
                  Bedrijfsbossen &#x2022; Schoolbossen &#x2022; Voedselbossen &#x2022;
                  Natuuropwaardering &#x2022; Dakboerderij
                </p>
              </div>
            </div>
            <div className="relative flex w-full flex-col items-center justify-center self-start rounded-b-lg">
              <img
                src="/images/algemeen/doe.png"
                alt="Relume placeholder image 1"
                className="rounded-b-lg"
              />

              {/* Hover overlay with button */}
              <motion.div
                className="absolute inset-0 bg-black/60 backdrop-blur-sm rounded-b-lg flex items-center justify-center"
                initial={{ opacity: 0 }}
                animate={{
                  opacity: hoveredCard === 'forestforward' ? 1 : 0,
                  transition: { duration: 0.3, ease: "easeOut" }
                }}
              >
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{
                    y: hoveredCard === 'forestforward' ? 0 : 20,
                    opacity: hoveredCard === 'forestforward' ? 1 : 0,
                    transition: { duration: 0.3, delay: 0.1, ease: "easeOut" }
                  }}
                >
                  <Button1
                    href="/forestforward"
                    variant="filled"
                    size="md"
                  >
                    Ontdek Forest Forward
                  </Button1>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
          <motion.div
            className="group relative flex flex-col border border-border rounded-lg overflow-hidden cursor-pointer"
            variants={{
              hidden: { opacity: 0, y: 30 },
              visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
            }}
            whileHover={{
              scale: 1.02,
              transition: { duration: 0.3, ease: "easeOut" }
            }}
            onMouseEnter={() => setHoveredCard('storyforward')}
            onMouseLeave={() => setHoveredCard(null)}
          >
            <div className="flex flex-1 flex-col justify-center p-6 md:p-8">
              <div>
                <img
                  src="/brand/storyforward/full_logo_cut.png"
                  alt="Story Forward Logo"
                  className="h-12 md:h-12 lg:h-14 w-auto object-contain"
                />
                <h2 className="mb-3 mt-3 text-2xl font-semibold md:mb-4 md:text-3xl md:leading-[1.3] lg:text-4xl">
                  INSPIREER
                </h2>
                <p>
                  Storytelling &#x2022; Mediarelaties &#x2022; Strategische &#x2022; communicatie &#x2022;
                  Trainingen &#x2022; Branding &#x2022; webdevelopment
                </p>
              </div>
            </div>
            <div className="relative flex w-full flex-col items-center justify-center self-start rounded-b-lg">
              <img
                src="/images/algemeen/inspireer.png"
                alt="Relume placeholder image 1"
                className="rounded-b-lg"
              />

              {/* Hover overlay with button */}
              <motion.div
                className="absolute inset-0 bg-black/60 backdrop-blur-sm rounded-b-lg flex items-center justify-center"
                initial={{ opacity: 0 }}
                animate={{
                  opacity: hoveredCard === 'storyforward' ? 1 : 0,
                  transition: { duration: 0.3, ease: "easeOut" }
                }}
              >
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{
                    y: hoveredCard === 'storyforward' ? 0 : 20,
                    opacity: hoveredCard === 'storyforward' ? 1 : 0,
                    transition: { duration: 0.3, delay: 0.1, ease: "easeOut" }
                  }}
                >
                  <Button1
                    href="/storyforward"
                    variant="filled"
                    size="md"
                  >
                    Ontdek Story Forward
                  </Button1>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
          <motion.div
            className="group relative flex flex-col border border-border rounded-lg overflow-hidden cursor-pointer"
            variants={{
              hidden: { opacity: 0, y: 30 },
              visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
            }}
            whileHover={{
              scale: 1.02,
              transition: { duration: 0.3, ease: "easeOut" }
            }}
            onMouseEnter={() => setHoveredCard('lagom')}
            onMouseLeave={() => setHoveredCard(null)}
          >
            <div className="flex flex-1 flex-col justify-center p-6 md:p-8">
              <div>
                <img
                  src="/brand/lagom/full_logo_cut.png"
                  alt="Lagom Logo"
                  className="h-12 md:h-12 lg:h-14 w-auto object-contain"
                />
                <h2 className="mb-3 mt-3 text-2xl font-semibold md:mb-4 md:text-3xl md:leading-[1.3] lg:text-4xl">
                  VERBIND
                </h2>
                <p>
                  Impact events &#x2022; Familiedagen &#x2022; Workshops &#x2022; Teambuildings &#x2022;
                  Keynotes
                </p>
              </div>
            </div>
            <div className="relative flex w-full flex-col items-center justify-center self-start rounded-b-lg">
              <img
                src="/images/algemeen/verbind.png"
                alt="Relume placeholder image 1"
                className="rounded-b-lg"
              />

              {/* Hover overlay with button */}
              <motion.div
                className="absolute inset-0 bg-black/60 backdrop-blur-sm rounded-b-lg flex items-center justify-center"
                initial={{ opacity: 0 }}
                animate={{
                  opacity: hoveredCard === 'lagom' ? 1 : 0,
                  transition: { duration: 0.3, ease: "easeOut" }
                }}
              >
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{
                    y: hoveredCard === 'lagom' ? 0 : 20,
                    opacity: hoveredCard === 'lagom' ? 1 : 0,
                    transition: { duration: 0.3, delay: 0.1, ease: "easeOut" }
                  }}
                >
                  <Button1
                    href="/lagom"
                    variant="filled"
                    size="md"
                  >
                    Ontdek Lagom
                  </Button1>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
