# ServiceListing Component

An interactive component for displaying services with big abstract titles and mouse-following hover cards. Creates a clean, modern experience with smooth Framer Motion animations.

## Features

- 🎨 **Theme-aware**: Automatically adapts to your theme system (homepage, forestforward, lagom, storyforward)
- 🖱️ **Mouse-following cards**: Hover cards that follow your cursor for maximum interactivity
- ✨ **Smooth animations**: Uses Framer Motion for professional hover effects and transitions
- 📱 **Responsive**: Works perfectly on all screen sizes with adaptive typography
- 🔧 **Configurable**: Highly customizable through props
- ♿ **Accessible**: Built with accessibility in mind
- 🎯 **Abstract design**: Big, bold titles with clean minimal aesthetic

## Basic Usage

```jsx
import { ServiceListing } from "@/app/_components/shared/ServiceListing";

export function MyPage() {
  return (
    <ServiceListing 
      title="Our Services"
      subtitle="Discover how we can help your organization"
    />
  );
}
```

## Custom Services

```jsx
const myServices = [
  {
    id: "service-1",
    title: "Consulting",
    abstractTitle: "BEGELEIDEN",
    description: "Professional guidance to help your organization achieve its sustainability goals.",
    href: "/services/consulting"
  },
  {
    id: "service-2",
    title: "Workshops",
    abstractTitle: "LEREN",
    description: "Interactive workshops that bring your team together around sustainability.",
    href: "/services/workshops"
  }
];

<ServiceListing
  services={myServices}
  title="Our Services"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `services` | Array | `[]` | Array of service objects (see Service Object below) |
| `title` | String | `"Onze Diensten"` | Main heading text |
| `subtitle` | String | `""` | Optional subtitle text |
| `className` | String | `""` | Additional CSS classes |
| `showTitle` | Boolean | `true` | Whether to show the title section |

## Service Object

Each service in the `services` array should have:

```javascript
{
  id: "unique-id",              // Required: Unique identifier
  title: "Service Name",        // Required: Service title (used in hover card)
  abstractTitle: "ABSTRACT",    // Optional: Big abstract title (defaults to title.toUpperCase())
  description: "...",           // Required: Detailed description for hover card
  href: "/link/to/service"      // Optional: Link URL
}
```

## Theme Integration

The component automatically uses your theme system. To use it in different themes:

```jsx
{/* Forest Forward theme */}
<div className="theme-forestforward">
  <ServiceListing services={forestServices} />
</div>

{/* Lagom theme */}
<div className="theme-lagom">
  <ServiceListing services={lagomServices} />
</div>
```

## Design Features

- **Big Abstract Titles**: Services displayed as large, bold typography (4xl to 9xl responsive)
- **Mouse-Following Cards**: Hover cards that track cursor movement for interactive experience
- **Minimal Layout**: Clean vertical list with subtle borders and spacing
- **Smooth Transitions**: Title slides right on hover, color changes to theme link color

## Animation Details

- **Staggered entrance**: Services animate in with a 0.1s delay between each
- **Title hover**: Slides 20px to the right and changes color
- **Mouse tracking**: Card follows cursor with 20px offset for comfortable viewing
- **Spring physics**: Hover cards use spring animation for natural movement
- **Fast transitions**: Quick 0.2s animations for responsive feel

## Accessibility

- Proper semantic HTML structure
- Keyboard navigation support
- Screen reader friendly
- High contrast theme support

## Examples

See `src/app/_components/examples/ServiceListingExample.jsx` for complete usage examples.
