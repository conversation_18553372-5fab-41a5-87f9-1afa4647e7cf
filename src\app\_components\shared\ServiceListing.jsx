"use client";

import { motion, AnimatePresence } from "framer-motion";
import React, { useState, useEffect, useRef } from "react";

export function ServiceListing({
  services = [],
  title = "Onze Diensten",
  subtitle = "",
  className = "",
  showTitle = true
}) {
  const [hoveredService, setHoveredService] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef(null);

  // Default services if none provided
  const defaultServices = [
    {
      id: "service-1",
      title: "Strategy",
      abstractTitle: "STRATEGIE",
      description: "Ontwikkel een duurzame strategie die past bij jouw organisatie en doelen.",
      href: "/diensten/strategie"
    },
    {
      id: "service-2",
      title: "Innovation",
      abstractTitle: "INNOVATIE",
      description: "Innovatieve oplossingen die jouw bedrijf naar de volgende level tillen.",
      href: "/diensten/innovatie"
    },
    {
      id: "service-3",
      title: "Transformation",
      abstractTitle: "TRANSFORMATIE",
      description: "Begeleid je organisatie door complexe veranderingsprocessen.",
      href: "/diensten/transformatie"
    },
    {
      id: "service-4",
      title: "Growth",
      abstractTitle: "GROEI",
      description: "Duurzame groeistrategieën die waarde creëren voor alle stakeholders.",
      href: "/diensten/groei"
    }
  ];

  const serviceData = services.length > 0 ? services : defaultServices;

  // Track mouse position for floating card (viewport coordinates for fixed positioning)
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({
        x: e.clientX,
        y: e.clientY
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <section
      ref={containerRef}
      className={`relative px-[5%] py-16 md:py-24 lg:py-28 ${className}`}
    >
      <div className="container">
        {showTitle && (
          <motion.div
            className="mb-12 md:mb-16 lg:mb-20 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold text-text-primary md:text-7xl lg:text-8xl mb-6">
              {title}
            </h2>
            {subtitle && (
              <p className="text-lg text-text-secondary max-w-3xl mx-auto md:text-xl">
                {subtitle}
              </p>
            )}
          </motion.div>
        )}

        {/* Service List */}
        <motion.div
          className="space-y-2 md:space-y-3"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={{
            hidden: {},
            visible: {
              transition: {
                staggerChildren: 0.1
              }
            }
          }}
        >
          {serviceData.map((service, index) => (
            <motion.div
              key={service.id}
              className="group cursor-pointer py-4 md:py-6 text-center relative"
              variants={{
                hidden: { opacity: 0, x: -30 },
                visible: { opacity: 1, x: 0, transition: { duration: 0.6 } }
              }}
              onMouseEnter={() => setHoveredService(service.id)}
              onMouseLeave={() => setHoveredService(null)}
              whileHover={{
                scale: 1.05,
                transition: { duration: 0.3, ease: "easeOut" }
              }}
            >
              <h3 className="text-4xl md:text-6xl lg:text-8xl xl:text-9xl font-bold text-text-primary group-hover:text-link transition-colors duration-300 tracking-tight">
                {service.abstractTitle || service.title.toUpperCase()}
              </h3>

              {/* Short separator line */}
              {index < serviceData.length - 1 && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-16 md:w-20 h-px bg-border-25"></div>
              )}
            </motion.div>
          ))}
        </motion.div>

        {/* Floating Card that follows mouse with physics */}
        <AnimatePresence>
          {hoveredService && (
            <motion.div
              className="fixed z-[9999] pointer-events-none bg-red-500 text-white p-4 rounded"
              style={{
                left: mousePosition.x + 20,
                top: mousePosition.y - 60,
              }}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <div>
                {(() => {
                  const service = serviceData.find(s => s.id === hoveredService);
                  return service ? (
                    <div>
                      <h4 className="font-semibold mb-2">
                        {service.title}
                      </h4>
                      <p className="text-sm">
                        {service.description}
                      </p>
                    </div>
                  ) : (
                    <div>Service not found: {hoveredService}</div>
                  );
                })()}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
}
