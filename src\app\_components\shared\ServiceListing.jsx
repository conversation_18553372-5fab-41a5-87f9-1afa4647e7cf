"use client";

import { motion, AnimatePresence } from "framer-motion";
import React, { useState, useEffect, useRef } from "react";
import { Button1 } from "./Button1";

export function ServiceListing({
  services = [],
  title = "Onze Diensten",
  subtitle = "",
  className = "",
  showTitle = true
}) {
  const [hoveredService, setHoveredService] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef(null);

  // Default services if none provided
  const defaultServices = [
    {
      id: "service-1",
      title: "Strategy",
      abstractTitle: "STRATEGIE",
      description: "Ontwikkel een duurzame strategie die past bij jouw organisatie en doelen.",
      href: "/diensten/strategie",
      color: "from-blue-500 to-purple-600"
    },
    {
      id: "service-2",
      title: "Innovation",
      abstractTitle: "INNOVATIE",
      description: "Innovatieve oplossingen die jouw bedrijf naar de volgende level tillen.",
      href: "/diensten/innovatie",
      color: "from-green-500 to-teal-600"
    },
    {
      id: "service-3",
      title: "Transformation",
      abstractTitle: "TRANSFORMATIE",
      description: "Begeleid je organisatie door complexe veranderingsprocessen.",
      href: "/diensten/transformatie",
      color: "from-orange-500 to-red-600"
    },
    {
      id: "service-4",
      title: "Growth",
      abstractTitle: "GROEI",
      description: "Duurzame groeistrategieën die waarde creëren voor alle stakeholders.",
      href: "/diensten/groei",
      color: "from-purple-500 to-pink-600"
    }
  ];

  const serviceData = services.length > 0 ? services : defaultServices;

  // Track mouse position for floating card
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setMousePosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
      return () => container.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  return (
    <section
      ref={containerRef}
      className={`relative px-[5%] py-16 md:py-24 lg:py-28 ${className}`}
    >
      <div className="container">
        {showTitle && (
          <motion.div
            className="mb-12 md:mb-16 lg:mb-20 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold text-text-primary md:text-7xl lg:text-8xl mb-6">
              {title}
            </h2>
            {subtitle && (
              <p className="text-lg text-text-secondary max-w-3xl mx-auto md:text-xl">
                {subtitle}
              </p>
            )}
          </motion.div>
        )}

        {/* Service List */}
        <motion.div
          className="space-y-4 md:space-y-6 lg:space-y-8"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={{
            hidden: {},
            visible: {
              transition: {
                staggerChildren: 0.1
              }
            }
          }}
        >
          {serviceData.map((service) => (
            <motion.div
              key={service.id}
              className="group cursor-pointer py-6 md:py-8 lg:py-12 border-b border-border-25 last:border-b-0"
              variants={{
                hidden: { opacity: 0, x: -30 },
                visible: { opacity: 1, x: 0, transition: { duration: 0.6 } }
              }}
              onMouseEnter={() => setHoveredService(service.id)}
              onMouseLeave={() => setHoveredService(null)}
              whileHover={{
                x: 20,
                transition: { duration: 0.3, ease: "easeOut" }
              }}
            >
              <h3 className="text-4xl md:text-6xl lg:text-8xl xl:text-9xl font-bold text-text-primary group-hover:text-link transition-colors duration-300 tracking-tight">
                {service.abstractTitle || service.title.toUpperCase()}
              </h3>
            </motion.div>
          ))}
        </motion.div>

        {/* Floating Card that follows mouse */}
        <AnimatePresence>
          {hoveredService && (
            <motion.div
              className="fixed z-50 pointer-events-none"
              style={{
                left: mousePosition.x + 20,
                top: mousePosition.y - 100,
              }}
              initial={{ opacity: 0, scale: 0.8, y: 20 }}
              animate={{
                opacity: 1,
                scale: 1,
                y: 0,
                transition: {
                  duration: 0.2,
                  ease: "easeOut"
                }
              }}
              exit={{
                opacity: 0,
                scale: 0.8,
                y: 20,
                transition: { duration: 0.15 }
              }}
            >
              <div className="bg-background-alternative text-text-alternative p-6 rounded-lg shadow-xlarge border border-border-alternative max-w-sm">
                {(() => {
                  const service = serviceData.find(s => s.id === hoveredService);
                  return service ? (
                    <div>
                      <h4 className="text-xl font-semibold mb-3">
                        {service.title}
                      </h4>
                      <p className="text-sm leading-relaxed mb-4 opacity-90">
                        {service.description}
                      </p>
                      {service.href && (
                        <Button1
                          href={service.href}
                          variant="transparent"
                          size="sm"
                          className="text-text-alternative border-text-alternative hover:bg-text-alternative hover:text-background-alternative"
                        >
                          Meer info
                        </Button1>
                      )}
                    </div>
                  ) : null;
                })()}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
}
