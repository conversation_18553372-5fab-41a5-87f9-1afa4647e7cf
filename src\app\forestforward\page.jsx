import React from "react";
import { Navbar2 } from "./_components/Navbar";
import { Footer2 } from "../_components/Footer";
import { Header64 } from "./_components/home/<USER>";
import { Logo3 } from "../_components/shared/Logo3";
import { Stats41 } from "./_components/home/<USER>";
import { Cta13 } from "./_components/home/<USER>";
import { Blog40 } from "./_components/home/<USER>";
import { Cta7 } from "./_components/home/<USER>";
import { Layout394 } from "../_components/shared/EcoLayout";
import { Gallery10 } from "./_components/home/<USER>";
import { Header114 } from "./_components/home/<USER>";
import { ServiceListing } from "../_components/shared/ServiceListing";


const myServices = [
  {
    id: "service-1",
    title: "Consulting",
    description: "Professional guidance to help your organization achieve its sustainability goals.",
    href: "/services/consulting",
    icon: "🎯"
  },
  {
    id: "service-2", 
    title: "Workshops",
    description: "Interactive workshops that bring your team together around sustainability.",
    href: "/services/workshops",
    icon: "🛠️"
  }
];

export default function Page() {
  return (
    <div>
      <Navbar2 />
      <Header114 />
      <ServiceListing 
        services={myServices}
        columns="md:grid-cols-2"
      />
      {/* <Header64 /> */}
      <Logo3 />
      <Stats41 />
      <Cta13 />
      <Blog40 />
      <Cta7 />
      <Layout394 />
      <Gallery10 />
      <Footer2 />
    </div>
  );
}
