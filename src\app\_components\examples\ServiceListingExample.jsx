"use client";

import React from "react";
import { ServiceListing } from "../shared/ServiceListing";

export function ServiceListingExample() {
  // Example service data for Forest Forward
  const forestForwardServices = [
    {
      id: "start2forest",
      title: "Start2Forest",
      abstractTitle: "BEGINNEN",
      description: "Begin je duurzame reis met onze Start2Forest programma's. We helpen organisaties de eerste stappen te zetten naar een groenere toekomst.",
      href: "/forestforward/start2forest"
    },
    {
      id: "boscompensatie",
      title: "Boscompensatie",
      abstractTitle: "COMPENSEREN",
      description: "Compenseer je CO2-uitstoot door het planten van bomen. Een directe en meetbare bijdrage aan een betere wereld.",
      href: "/forestforward/boscompensatie"
    },
    {
      id: "team-forest",
      title: "Team Forest",
      abstractTitle: "SAMENWERKEN",
      description: "Versterk teambuilding door samen bomen te planten. Combineer samenwerking met maatschappelijke impact.",
      href: "/forestforward/team-forest"
    },
    {
      id: "forest-strategy",
      title: "Forest Strategy",
      abstractTitle: "STRATEGISEREN",
      description: "Ontwikkel een duurzame strategie voor je organisatie met onze expertise in natuurbeheer en biodiversiteit.",
      href: "/forestforward/strategie"
    }
  ];

  // Example service data for Lagom
  const lagomServices = [
    {
      id: "duurzame-events",
      title: "Duurzame Events",
      abstractTitle: "EVENEMENTEN",
      description: "Organiseer events die niet alleen inspireren, maar ook een positieve impact hebben op mens en milieu.",
      href: "/lagom/events"
    },
    {
      id: "talks-walks",
      title: "Talks & Walks",
      abstractTitle: "WANDELEN",
      description: "Combineer inspirerende gesprekken met wandelingen in de natuur voor maximale impact en verbinding.",
      href: "/lagom/talks-walks"
    },
    {
      id: "workshops",
      title: "Duurzame Workshops",
      abstractTitle: "WORKSHOPS",
      description: "Interactieve workshops die je team samenbrengen rond duurzaamheid en maatschappelijke verantwoordelijkheid.",
      href: "/lagom/workshops"
    }
  ];

  return (
    <div className="space-y-20">
      {/* Default example */}
      <ServiceListing
        title="Onze Diensten"
        subtitle="Hover over de diensten om meer te ontdekken"
      />

      {/* Forest Forward themed example */}
      <div className="theme-forestforward">
        <ServiceListing
          services={forestForwardServices}
          title="Forest Forward"
          subtitle="Samen groeien we naar een biodiversere, duurzame toekomst"
        />
      </div>

      {/* Lagom themed example */}
      <div className="theme-lagom">
        <ServiceListing
          services={lagomServices}
          title="Lagom Events"
          subtitle="Niet te veel, niet te weinig, gewoon goed - dat is Lagom"
        />
      </div>

      {/* Minimal version without title */}
      <ServiceListing
        services={forestForwardServices.slice(0, 3)}
        showTitle={false}
        className="bg-background-primary"
      />
    </div>
  );
}
