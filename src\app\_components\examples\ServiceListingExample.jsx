"use client";

import React from "react";
import { ServiceListing } from "../shared/ServiceListing";

export function ServiceListingExample() {
  // Example service data for Forest Forward
  const forestForwardServices = [
    {
      id: "start2forest",
      title: "Start2Forest",
      abstractTitle: "BEGINNEN",
      description: "Begin je duurzame reis met onze Start2Forest programma's. We helpen organisaties de eerste stappen te zetten naar een groenere toekomst.",
      href: "/forestforward/start2forest"
    },
    {
      id: "boscompensatie",
      title: "Boscompensatie",
      abstractTitle: "COMPENSEREN",
      description: "Compenseer je CO2-uitstoot door het planten van bomen. Een directe en meetbare bijdrage aan een betere wereld.",
      href: "/forestforward/boscompensatie"
    },
    {
      id: "team-forest",
      title: "Team Forest",
      abstractTitle: "SAMENWERKEN",
      description: "Versterk teambuilding door samen bomen te planten. Combineer samenwerking met maatschappelijke impact.",
      href: "/forestforward/team-forest"
    }
  ];

  return (
    <div className="space-y-20">
      {/* Default example */}
      <ServiceListing 
        title="Onze Diensten"
        subtitle="Hover over de diensten om meer te ontdekken"
      />

      {/* Forest Forward themed example */}
      <div className="theme-forestforward">
        <ServiceListing 
          services={forestForwardServices}
          title="Forest Forward"
          subtitle="Samen groeien we naar een biodiversere, duurzame toekomst"
        />
      </div>

      {/* Minimal version without title */}
      <ServiceListing 
        services={forestForwardServices.slice(0, 2)}
        showTitle={false}
        className="bg-background-primary"
      />
    </div>
  );
}
