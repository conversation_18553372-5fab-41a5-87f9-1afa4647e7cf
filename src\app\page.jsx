import React from "react";
import { Navbar2 } from "./_components/Navbar";
import { Layout394 } from "./_components/shared/EcoLayout";
import { Logo3 } from "./_components/shared/Logo3";
import { Blog36 } from "./_components/home/<USER>";
import { Gallery1 } from "./_components/shared/Gallery1";
import { Footer2 } from "./_components/Footer";
import { Header114 } from "./_components/home/<USER>";

export default function Page() {
  // Define images for the homepage gallery
  const galleryImages = [
    {
      src: "/images/algemeen/moodboard 1.png",
      alt: "Moodboard image 1"
    },
    {
      src: "/images/algemeen/moodboard 2.png",
      alt: "Moodboard image 2"
    },
    {
      src: "/images/algemeen/moodboard 3.png",
      alt: "Moodboard image 3"
    },
    {
      src: "/images/algemeen/moodboard 4.png",
      alt: "Moodboard image 4"
    },
    {
      src: "/images/algemeen/moodboard 5.png",
      alt: "Moodboard image 5"
    },
    {
      src: "/images/algemeen/moodboard 6.png",
      alt: "Moodboard image 6"
    },
    {
      src: "/images/algemeen/moodboard 7.png",
      alt: "Moodboard image 7"
    }
  ];

  return (
    <div className="theme-homepage">
      <Navbar2 />
      <Header114 />
      <Layout394 />
      <Logo3 />
      <Blog36 />
      <Gallery1
        images={galleryImages}
        title="Moodboard"
      />
      <Footer2 />
    </div>
  );
}
